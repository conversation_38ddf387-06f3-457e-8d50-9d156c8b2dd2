/* ============================================
   TAILWIND CSS v4 IMPORTS & CONFIGURATION
   ============================================ */
@import "tailwindcss";
@import "tw-animate-css";

/* Custom dark mode variant for Tailwind v4 */
@custom-variant dark (&:is(.dark *));

/* Tailwind v4 theme configuration - maps CSS variables to Tailwind classes */
@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

/* ============================================
   CSS CUSTOM PROPERTIES (DESIGN TOKENS)
   ============================================ */

/* Light mode color palette (Enhanced for beautiful light mode) */
:root {
  --radius: 0.625rem;
  --background: 0 0% 99%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 160 84% 39%;
  --primary-foreground: 210 40% 98%;
  --secondary: 160 20% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 160 15% 97%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 160 25% 95%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --border: 160 20% 90%;
  --input: 160 15% 94%;
  --ring: 160 84% 39%;
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;
  --sidebar: 160 30% 98%;
  --sidebar-foreground: 222.2 84% 4.9%;
  --sidebar-primary: 160 84% 39%;
  --sidebar-primary-foreground: 210 40% 98%;
  --sidebar-accent: 160 25% 95%;
  --sidebar-accent-foreground: 222.2 84% 4.9%;
  --sidebar-border: 160 20% 88%;
  --sidebar-ring: 160 84% 39%;
  
  /* Light mode specific variables */
  --light-glass-bg: rgba(255, 255, 255, 0.85);
  --light-glass-border: rgba(16, 185, 129, 0.15);
  --light-spotlight-color: rgba(16, 185, 129, 0.08);
  --light-card-shadow: rgba(16, 185, 129, 0.1);
  --light-text-primary: #1f2937;
  --light-text-secondary: #6b7280;
  --light-text-muted: #9ca3af;
}

/* Dark mode color palette (OKLCH format for better color accuracy) */
.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

/* ============================================
   TAILWIND BASE LAYER OVERRIDES
   ============================================ */
@layer base {
  /* Global element defaults */
  * {
    @apply border-border;
  }
  
  /* Add outline for keyboard navigation but remove for mouse users */
  *:focus-visible {
    @apply outline-ring/50 outline-2 outline-offset-2 outline;
  }
  
  /* Remove outline for query input specifically */
  #query:focus-visible {
    outline: none !important;
  }

  /* Primary background color for all pages */
  body {
    @apply bg-background antialiased;
  }

  /* Prevent initial flash and enable smooth color scheme transitions */
  html {
    color-scheme: light dark;
  }
}

/* ============================================
   THEME TRANSITION SYSTEM
   ============================================ */

/* ZenUI-style Enhanced Dark Mode Transitions */
@supports (view-transition-name: none) {
  /* Disable default view transition animations */
  ::view-transition-old(root),
  ::view-transition-new(root) {
    animation: none;
    mix-blend-mode: normal;
  }

  ::view-transition-old(root) {
    z-index: 1;
  }

  ::view-transition-new(root) {
    z-index: 2;
  }

  /* Ensure smooth blending between themes */
  ::view-transition-group(root) {
    animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  /* Prevent any flickering during transition */
  html {
    view-transition-name: root;
  }
}

/* Enhanced view transition support for expanding circle effect */
@supports (view-transition-name: none) {
  /* Custom transition for theme changes */
  ::view-transition-new(root) {
    animation: none;
    clip-path: circle(0px at 50% 50%);
  }

  ::view-transition-old(root) {
    animation: none;
  }
}

/* Smooth transitions for all theme-aware elements */
* {
  transition: background-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    border-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    fill 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    stroke 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    box-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Prevent transition on page load */
.no-transition * {
  transition: none !important;
}

/* ZenUI-style enhanced theme toggle animations */
.theme-toggle-button {
  position: relative;
  overflow: hidden;
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Enhanced smooth icon transitions */
.theme-toggle-button svg,
.theme-toggle-button [data-lucide] {
  transition: transform 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    opacity 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    scale 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: center;
  will-change: transform, opacity, scale;
}

/* Prevent any jank during transitions */
.theme-toggle-button svg * {
  transition: fill 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    stroke 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Smooth icon state transitions with staggered timing */
.theme-toggle-button .lucide-sun {
  transition-delay: 0ms;
}

.theme-toggle-button .lucide-moon {
  transition-delay: 50ms;
}

/* Enhanced hover effects for smoother interaction */
.theme-toggle-button:hover svg {
  transform: scale(1.05);
}

.theme-toggle-button:active svg {
  transform: scale(0.95);
  transition-duration: 0.1s;
}

/* ZenUI-style contracting circle effect for light to dark */
.theme-toggle-button::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 400px;
  height: 400px;
  background: radial-gradient(
    circle,
    rgba(0, 0, 0, 0.95) 0%,
    rgba(0, 0, 0, 0.7) 40%,
    transparent 70%
  );
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: transform 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  pointer-events: none;
  opacity: 0;
}

/* ZenUI-style expanding circle effect for dark to light */
.theme-toggle-button::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 400px;
  height: 400px;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.8) 40%,
    transparent 70%
  );
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: transform 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  pointer-events: none;
  opacity: 0;
}

/* Active states for ZenUI-style transitions */
.theme-toggle-button.switching-to-dark::before {
  transform: translate(-50%, -50%) scale(1.2);
  opacity: 1;
}

.theme-toggle-button.switching-to-light::after {
  transform: translate(-50%, -50%) scale(1.2);
  opacity: 1;
}

/* Subtle button scale effect */
.theme-toggle-button:active {
  transform: scale(0.98);
}

/* Enhanced focus states */
.theme-toggle-button:focus-visible {
  outline: 2px solid rgb(34, 197, 94);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.1);
}

/* ============================================
   SCROLLBAR STYLING
   ============================================ */

/* Global scrollbar styles for all elements */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.12);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.18);
}

/* Dark mode scrollbar styles */
.dark ::-webkit-scrollbar-track {
  background: rgba(0, 255, 185, 0.05);
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 185, 0.1);
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 185, 0.15);
}

/* Table parent container with custom scrollbar - USED IN: data tables */
.tb-parent {
  --sb-track-color: #d8dadbda;
  --sb-thumb-color: #9fa1a086;
  --sb-size: 20px;
  @apply bg-white/10 dark:bg-[#011f17]/40;
  backdrop-filter: blur(8px);
}

.tb-parent::-webkit-scrollbar {
  width: var(--sb-size);
}

.tb-parent::-webkit-scrollbar-track {
  background: var(--sb-track-color);
  border-radius: 9px;
}

.tb-parent::-webkit-scrollbar-thumb {
  background: var(--sb-thumb-color);
  border-radius: 9px;
}

/* ============================================
   CUSTOM COMPONENT STYLES
   ============================================ */

/* Card gradient component - USED IN: various cards throughout the app */
.card-gradient {
  @apply bg-gradient-to-br from-white/90 to-emerald-50/60 dark:from-[#011f17]/80 dark:to-[#011f17]/40;
  backdrop-filter: blur(12px);
  box-shadow: 0 8px 32px rgba(16, 185, 129, 0.08), 0 4px 16px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(16, 185, 129, 0.12);
}

.dark .card-gradient {
  box-shadow: 0 0 20px rgba(0, 255, 185, 0.03);
  border: 1px solid rgba(54, 62, 60, 0.1);
}

/* Light mode glass morphism effects */
.glass-light {
  background: var(--light-glass-bg);
  backdrop-filter: blur(16px);
  border: 1px solid var(--light-glass-border);
  box-shadow: 0 8px 32px var(--light-card-shadow), 
              0 1px 0 rgba(255, 255, 255, 0.5) inset;
}

/* Enhanced light mode spotlight */
.spotlight-light {
  background: radial-gradient(
    ellipse at center,
    var(--light-spotlight-color) 0%,
    transparent 70%
  );
}

/* Light mode system cards */
.system-card-light {
  background: linear-gradient(
    158.39deg,
    rgba(255, 255, 255, 0.95) 14.19%,
    rgba(255, 255, 255, 0.85) 50.59%,
    rgba(255, 255, 255, 0.90) 68.79%,
    rgba(255, 255, 255, 0.95) 105.18%
  );
  border: 1.2px solid rgba(16, 185, 129, 0.2);
  box-shadow: 0 8px 32px rgba(16, 185, 129, 0.12),
              0 1px 0 rgba(255, 255, 255, 0.8) inset;
}

/* Button component styles - USED IN: custom buttons throughout the app */
.btn-primary {
  @apply bg-gradient-to-r from-[#5BE49B] to-[#00A76F] text-white font-medium rounded-lg shadow-md hover:shadow-lg transition-all transform hover:-translate-y-0.5;
}

.btn-primary:disabled {
  @apply from-gray-400 to-gray-500 cursor-not-allowed;
}

/* Grid background utilities for CompanyTreeView */
.bg-grid-gray-200\/50 {
  background-image: linear-gradient(rgba(229, 231, 235, 0.5) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(229, 231, 235, 0.5) 1px, transparent 1px);
  background-size: 20px 20px;
}

.dark .bg-grid-gray-800\/50 {
  background-image: linear-gradient(rgba(31, 41, 55, 0.5) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(31, 41, 55, 0.5) 1px, transparent 1px);
  background-size: 20px 20px;
}

.btn-secondary {
  @apply bg-[#f0f9f5] hover:bg-[#e1f4ea] dark:bg-[#011f17] dark:hover:bg-[#012920] text-[#00bf6f] dark:text-[#00bf6f];
}

.btn-outline {
  @apply border border-gray-200 dark:border-[#012920] bg-white/50 dark:bg-[#011f17]/50 hover:bg-gray-50 dark:hover:bg-[#012920] text-gray-700 dark:text-[#00bf6f];
}

/* Input gradient component - USED IN: custom form inputs */
.input-gradient {
  @apply bg-gradient-to-r from-gray-100/90 to-gray-50/80 dark:from-[#011f17]/90 dark:to-[#012920]/80;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 1px 0 0 rgba(255, 255, 255, 0.05);
}

.input-gradient::placeholder {
  @apply text-gray-500 dark:text-gray-400;
}

.input-gradient:focus {
  @apply bg-gradient-to-r from-white/95 to-gray-50/90 dark:from-[#012920]/95 dark:to-[#011f17]/90;
  border-color: rgba(0, 191, 111, 0.3);
  box-shadow: 0 0 0 1px rgba(0, 191, 111, 0.1),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.05);
}

/* Sidebar container - USED IN: main sidebar component */
.sidebar-container {
  background: rgba(255, 255, 255, 0.6);
  border-right: 1px solid #e5e7eb;
  backdrop-filter: blur(24px);
}

.dark .sidebar-container {
  background: rgba(16, 24, 28, 0.8);
  border-right: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(24px);
}

/* ============================================
   MARKDOWN CONTENT STYLING
   ============================================ */

/* Markdown body component - USED IN: FileQuery.tsx for displaying markdown content */
.markdown-body {
  color: rgb(31 41 55);
}

.dark .markdown-body {
  color: rgb(255 255 255);
}

/* Markdown headings */
.markdown-body h1 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-top: 1.2em;
  margin-bottom: 0.5em;
  color: rgb(17 24 39);
}

.markdown-body h2 {
  font-size: 1.25rem;
  font-weight: bold;
  margin-top: 1em;
  margin-bottom: 0.5em;
  color: rgb(17 24 39);
}

.markdown-body h3 {
  font-size: 1.1rem;
  font-weight: bold;
  margin-top: 0.8em;
  margin-bottom: 0.4em;
  color: rgb(17 24 39);
}

.dark .markdown-body h1,
.dark .markdown-body h2,
.dark .markdown-body h3,
.dark .markdown-body h4,
.dark .markdown-body h5,
.dark .markdown-body h6 {
  color: rgb(243 244 246);
}

/* Markdown lists */
.markdown-body ul,
.markdown-body ol {
  margin-left: 1.5em;
  margin-bottom: 1em;
}

.markdown-body li {
  margin-bottom: 0.3em;
}

/* Markdown paragraphs */
.markdown-body p {
  color: rgb(55 65 81);
}

.dark .markdown-body p {
  color: rgb(209 213 219);
}

/* Markdown code blocks */
.markdown-body code,
.markdown-body pre {
  background-color: rgb(243 244 246);
  color: rgb(31 41 55);
  border-radius: 4px;
  padding: 2px 6px;
  font-family: "Fira Mono", "Consolas", "Menlo", monospace;
}

.dark .markdown-body code {
  background-color: rgb(31 41 55);
  color: rgb(229 231 235);
}

.markdown-body pre {
  border: 1px solid rgb(229 231 235);
}

.dark .markdown-body pre {
  background-color: rgb(31 41 55);
  border: 1px solid rgb(55 65 81);
}

/* Markdown blockquotes */
.markdown-body blockquote {
  border-left: 4px solid rgb(209 213 219);
  background-color: rgb(249 250 251);
}

.dark .markdown-body blockquote {
  border-left: 4px solid rgb(75 85 99);
  background-color: rgb(31 41 55);
}

/* Markdown tables */
.markdown-body table {
  border: 1px solid rgb(229 231 235);
}

.dark .markdown-body table {
  border: 1px solid rgb(55 65 81);
}

.markdown-body th,
.markdown-body td {
  border: 1px solid rgb(229 231 235);
}

.dark .markdown-body th,
.dark .markdown-body td {
  border: 1px solid rgb(55 65 81);
}

/* ============================================
   SPOTLIGHT ANIMATION
   ============================================ */

/* Spotlight animation keyframes */
@keyframes spotlight {
  0% {
    opacity: 0;
    transform: translate(-50%, -60%) scale(0.3);
  }
  50% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(0.7);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -40%) scale(1);
  }
}

.animate-spotlight {
  animation: spotlight 3s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.5s 1 forwards;
}

/* Floating particles animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.4;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

/* ============================================
   UTILITY CLASSES
   ============================================ */

/* Custom utility classes - USED IN: Navbar.tsx for user tooltip */
.backdrop-blur-12 {
  backdrop-filter: blur(12px);
}

.w-70 {
  width: 280px;
}

.top-15 {
  top: 60px;
}

/* Radial gradient utility for company hierarchy */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}
